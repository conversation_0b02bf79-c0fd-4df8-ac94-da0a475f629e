package com.xyy.saas.inquiry.signature.server.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 字段位置配置类 - 用于PNG直接生成时的字段定位
 * 
 * <AUTHOR>
 */
@Slf4j
@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "inquiry.signature.field-position")
public class FieldPositionConfig {

    /**
     * 字段位置映射配置
     * key: 字段名称
     * value: 位置坐标 "x,y" 格式
     */
    private Map<String, String> positions = new HashMap<>();

    /**
     * 默认字体大小
     */
    private int defaultFontSize = 12;

    /**
     * 默认字体名称
     */
    private String defaultFontName = "SimSun";

    /**
     * 行间距
     */
    private int lineSpacing = 15;

    /**
     * 是否启用PNG直接生成
     */
    private boolean enablePngGeneration = true;

    /**
     * PNG生成失败时是否回退到PDF生成
     */
    private boolean fallbackToPdf = true;

    /**
     * 获取字段位置
     */
    public FieldPosition getFieldPosition(String fieldName) {
        String positionStr = positions.get(fieldName);
        if (positionStr == null || positionStr.trim().isEmpty()) {
            log.debug("字段 {} 未配置位置", fieldName);
            return null;
        }

        try {
            String[] parts = positionStr.split(",");
            if (parts.length != 2) {
                log.warn("字段 {} 位置配置格式错误: {}", fieldName, positionStr);
                return null;
            }

            int x = Integer.parseInt(parts[0].trim());
            int y = Integer.parseInt(parts[1].trim());
            return new FieldPosition(x, y);

        } catch (NumberFormatException e) {
            log.error("字段 {} 位置配置解析失败: {}", fieldName, positionStr, e);
            return null;
        }
    }

    /**
     * 字段位置类
     */
    @Data
    public static class FieldPosition {
        private final int x;
        private final int y;

        public FieldPosition(int x, int y) {
            this.x = x;
            this.y = y;
        }
    }

    /**
     * 初始化默认配置
     */
    public void initDefaultPositions() {
        if (positions.isEmpty()) {
            log.info("初始化默认字段位置配置");
            
            // 患者信息区域
            positions.put("patientName", "100,150");
            positions.put("patientAge", "300,150");
            positions.put("patientGender", "450,150");
            positions.put("patientPhone", "100,180");
            
            // 医生信息区域
            positions.put("doctorName", "100,220");
            positions.put("doctorTitle", "300,220");
            positions.put("department", "450,220");
            
            // 处方信息区域
            positions.put("prescriptionDate", "100,260");
            positions.put("diagnosis", "100,300");
            positions.put("medicine", "100,350");
            positions.put("dosage", "300,350");
            positions.put("usage", "450,350");
            
            // 签名区域
            positions.put("doctorSignature", "100,500");
            positions.put("pharmacistSignature", "300,500");
            
            log.info("默认字段位置配置初始化完成，共 {} 个字段", positions.size());
        }
    }
}
