package com.xyy.saas.inquiry.util;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.xyy.saas.inquiry.enums.file.FileTypeEnum;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.*;
import java.net.URL;
import java.net.URLConnection;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * 文件工具类
 *
 * @Author: cxy
 * @Date: 2025/4/8 10:54
 */
@Slf4j
public class FileUtils {

    /**
     * 文件下载（网络文件）
     *
     * @param fileUrl 文件url
     * @return 文件字节码
     */
    @SneakyThrows
    public static byte[] downLoadFile(String fileUrl) {
        if (StringUtils.isBlank(fileUrl)) {
            return new byte[0];
        }
        try {
            if (fileUrl.startsWith("http")) {
                HttpResponse response = HttpUtil.createGet(fileUrl).execute();
                return response.bodyBytes();
            } else {
                // 兼容base64
                if (fileUrl.startsWith("data:image")) {
                    return Base64.getDecoder().decode(fileUrl.substring(fileUrl.indexOf(",") + 1));
                }
                return FileUtil.readBytes(fileUrl);
            }
        } catch (Exception e) {
            log.error("文件下载失败, fileUrl:{}", fileUrl, e);
            throw e;
        }
    }

    /**
     * 文件下载（网络文件）转base64
     *
     * @param fileUrl 文件url
     * @return 文件base64
     */
    public static String downLoadFile2Base64(String fileUrl) {
        try {
            byte[] bytes = downLoadFile(fileUrl);
            return Base64.getEncoder().encodeToString(bytes);
        } catch (Exception e) {
            log.error("文件下载转base64失败, fileUrl:{}", fileUrl, e);
            return "";
        }
    }

    /**
     * 文件下载（网络文件）转base64
     *
     * @param fileUrl  文件url
     * @param fileType 文件类型 1-pdf 2-png 3-jpg
     * @return 文件base64
     */
    public static String downLoadFile2Base64(String fileUrl, Integer fileType) {
        try {
            byte[] bytes = downLoadFile(fileUrl);
            // 如果是PDF文件，先转换为PNG再转为base64
            if (FileTypeEnum.PDF.getCode().equals(fileType)) {
                try (ByteArrayInputStream pdfInputStream = new ByteArrayInputStream(bytes)) {
                    // 使用优化后的PDF转PNG方法
                    byte[] pngBytes = PdfBoxUtils.pdf2png2Optimized(pdfInputStream);
                    return Base64.getEncoder().encodeToString(pngBytes);
                } catch (Exception e) {
                    log.error("PDF转PNG失败, fileUrl:{}", fileUrl, e);
                    // 如果转换失败，仍然返回原PDF的base64
                    return Base64.getEncoder().encodeToString(bytes);
                }
            }
            return Base64.getEncoder().encodeToString(bytes);
        } catch (Exception e) {
            log.error("文件下载转base64失败, fileUrl:{}", fileUrl, e);
            return "";
        }
    }

    public static String downLoadFile2Base64WithFileName(String fileUrl) {
        try {
            byte[] bytes = downLoadFile(fileUrl);
            return "data:image/png;base64," + Base64.getEncoder().encodeToString(bytes);
        } catch (Exception e) {
            log.error("文件下载转base64失败, fileUrl:{}", fileUrl, e);
            return "";
        }
    }

    /**
     * 获取网络文件名称
     *
     * @param fileUrl 文件url
     * @return 文件名称
     */
    public static String getFileName(String fileUrl) {
        if (StrUtil.isBlank(fileUrl)) {
            return "";
        }
        try {
            URL url = new URL(fileUrl);
            URLConnection connection = url.openConnection();
            String fileName = connection.getHeaderField("Content-Disposition");
            if (StrUtil.isBlank(fileName)) {
                String path = url.getPath();
                return path.substring(path.lastIndexOf("/") + 1);
            }
            return fileName;
        } catch (Exception e) {
            log.error("获取网络文件名称失败, fileUrl:{}", fileUrl, e);
            return "";
        }
    }

    /**
     * 获取网络文件流
     *
     * @param fileUrl 文件url
     * @return 文件流
     */
    public static InputStream getFileInputStream(String fileUrl) {
        if (StrUtil.isBlank(fileUrl)) {
            return null;
        }
        try {
            if (fileUrl.startsWith("http")) {
                HttpResponse response = HttpUtil.createGet(fileUrl).execute();
                return new ByteArrayInputStream(response.bodyBytes());
            } else {
                // 兼容base64
                if (fileUrl.startsWith("data:image")) {
                    byte[] bytes = Base64.getDecoder().decode(fileUrl.substring(fileUrl.indexOf(",") + 1));
                    return new ByteArrayInputStream(bytes);
                }
                return FileUtil.getInputStream(fileUrl);
            }
        } catch (Exception e) {
            log.error("获取网络文件流失败, fileUrl:{}", fileUrl, e);
            return null;
        }
    }

    /**
     * 读取本地文件为字符串
     *
     * @param path 文件路径
     * @return 文件内容字符串
     */
    public static String readString(String path) {
        return FileUtil.readString(path, StandardCharsets.UTF_8);
    }
}