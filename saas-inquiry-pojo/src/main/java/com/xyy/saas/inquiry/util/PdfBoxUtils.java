package com.xyy.saas.inquiry.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.PDFRenderer;
import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.io.InputStream;
import java.util.concurrent.*;

/**
 * PDF处理工具类，优化版本
 */
@Slf4j
public class PdfBoxUtils {
    
    // 创建一个固定大小的线程池用于处理PDF转换任务
    private static final ExecutorService pdfProcessExecutor = new ThreadPoolExecutor(
        2,  // 核心线程数
        10, // 最大线程数
        60L, TimeUnit.SECONDS, // 空闲线程存活时间
        new LinkedBlockingQueue<>(100), // 任务队列
        new ThreadFactory() {
            private final AtomicInteger threadNumber = new AtomicInteger(1);
            
            @Override
            public Thread newThread(Runnable r) {
                Thread t = new Thread(r, "pdf-process-thread-" + threadNumber.getAndIncrement());
                t.setDaemon(false);
                return t;
            }
        },
        new ThreadPoolExecutor.CallerRunsPolicy() // 当线程池满时，由调用线程处理任务
    );
    
    /**
     * 优化版PDF转PNG方法，避免长时间阻塞
     * 
     * @param pdfInputStream PDF文件输入流
     * @param dpi 渲染DPI（建议不超过150）
     * @param maxWidth 最大宽度限制
     * @param maxHeight 最大高度限制
     * @param timeoutMillis 超时时间（毫秒）
     * @return 转换后的PNG图片字节数组
     * @throws Exception 转换异常或超时异常
     */
    public static byte[] pdf2png2Optimized(InputStream pdfInputStream, int dpi, int maxWidth, int maxHeight, long timeoutMillis) throws Exception {
        Callable<byte[]> task = () -> {
            PDDocument document = null;
            try {
                // 加载PDF文档
                document = PDDocument.load(pdfInputStream);
                
                // 创建PDF渲染器
                PDFRenderer pdfRenderer = new PDFRenderer(document);
                
                // 只渲染第一页以提高性能
                BufferedImage image = pdfRenderer.renderImageWithDPI(0, Math.min(dpi, 150));
                
                // 如果图片尺寸超过限制，则进行缩放
                if (image.getWidth() > maxWidth || image.getHeight() > maxHeight) {
                    double scaleWidth = (double) maxWidth / image.getWidth();
                    double scaleHeight = (double) maxHeight / image.getHeight();
                    double scale = Math.min(scaleWidth, scaleHeight);
                    
                    int newWidth = (int) (image.getWidth() * scale);
                    int newHeight = (int) (image.getHeight() * scale);
                    
                    // 使用快速缩放算法替代AreaAveragingScaleFilter
                    Image scaledImage = image.getScaledInstance(newWidth, newHeight, Image.SCALE_FAST);
                    
                    // 创建新的BufferedImage并使用Graphics2D绘制缩放后的图像
                    BufferedImage bufferedScaledImage = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_RGB);
                    Graphics2D g2d = bufferedScaledImage.createGraphics();
                    
                    // 设置渲染提示以使用快速渲染
                    g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_SPEED);
                    g2d.setRenderingHint(RenderingHints.KEY_COLOR_RENDERING, RenderingHints.VALUE_COLOR_RENDER_SPEED);
                    g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_NEAREST_NEIGHBOR);
                    g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_OFF);
                    g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_OFF);
                    
                    g2d.drawImage(scaledImage, 0, 0, null);
                    g2d.dispose();
                    
                    image = bufferedScaledImage;
                }
                
                // 转换为字节数组
                java.io.ByteArrayOutputStream baos = new java.io.ByteArrayOutputStream();
                ImageIO.write(image, "png", baos);
                return baos.toByteArray();
                
            } catch (IOException e) {
                log.error("PDF转换PNG失败", e);
                throw new RuntimeException("PDF转换PNG失败: " + e.getMessage(), e);
            } finally {
                if (document != null) {
                    try {
                        document.close();
                    } catch (IOException e) {
                        log.warn("关闭PDF文档时出错", e);
                    }
                }
            }
        };
        
        // 提交任务并设置超时时间
        Future<byte[]> future = pdfProcessExecutor.submit(task);
        try {
            return future.get(timeoutMillis, TimeUnit.MILLISECONDS);
        } catch (TimeoutException e) {
            future.cancel(true); // 取消任务
            log.warn("PDF转换超时，已取消任务");
            throw new Exception("PDF转换超时，超过" + timeoutMillis + "毫秒", e);
        } catch (ExecutionException e) {
            log.error("PDF转换执行异常", e);
            throw new Exception("PDF转换执行异常: " + e.getCause().getMessage(), e.getCause());
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("PDF转换任务被中断", e);
            throw new Exception("PDF转换任务被中断", e);
        }
    }
    
    /**
     * 简化版PDF转PNG方法，默认参数
     * 
     * @param pdfInputStream PDF文件输入流
     * @return 转换后的PNG图片字节数组
     * @throws Exception 转换异常或超时异常
     */
    public static byte[] pdf2png2Optimized(InputStream pdfInputStream) throws Exception {
        // 使用默认参数：DPI=100，最大宽高1200，超时时间30秒
        return pdf2png2Optimized(pdfInputStream, 100, 1200, 1200, 30000);
    }
    
    /**
     * 关闭线程池，释放资源
     */
    public static void shutdown() {
        if (pdfProcessExecutor != null && !pdfProcessExecutor.isShutdown()) {
            pdfProcessExecutor.shutdown();
            try {
                if (!pdfProcessExecutor.awaitTermination(60, TimeUnit.SECONDS)) {
                    pdfProcessExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                pdfProcessExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }
}